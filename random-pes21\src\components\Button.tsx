import React from 'react';
import { LucideIcon } from 'lucide-react';

interface ButtonProps {
  onClick: () => void;
  children: React.ReactNode;
  icon?: React.ReactElement;
  variant?: 'primary' | 'secondary';
  title?: string;
}

const Button: React.FC<ButtonProps> = ({
  onClick,
  children,
  icon,
  variant = 'primary',
  title
}) => {
  const baseClasses = "py-3 px-6 text-lg font-bold rounded-full shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 focus:outline-none flex items-center justify-center";
  const variantClasses = variant === 'primary'
    ? "gradient-yellow"
    : "bg-gray-200 text-gray-800 hover:bg-gray-300";

  return (
    <button
      onClick={onClick}
      className={`${baseClasses} ${variantClasses}`}
      style={{
        minWidth: '160px',
        cursor: 'pointer'
      }}
      title={title}
    >
      {icon && <span className="mr-2">{icon}</span>}
      {children}
    </button>
  );
};

export default Button;