import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { User, Users, Image, Languages } from 'lucide-react';
import { BackgroundImage } from '../types';
import { useLocalStorage } from '../hooks/useLocalStorage';
import { useGeminiAPI } from '../hooks/useGeminiAPI';
import { STORAGE_KEYS } from '../constants/quiz';
import { categories, languages, translations } from '../data/quizData';
import { QuizQuestion, AnswerOption, Language } from '../types/quiz';
import { getContainerStyle, getContainerClasses } from '../utils/quizStyles';

const Quiz: React.FC = () => {
  // Use custom hooks for state management
  const [background] = useLocalStorage<BackgroundImage | null>(STORAGE_KEYS.BACKGROUND, null);
  const [useBackground, setUseBackground] = useLocalStorage<boolean>(STORAGE_KEYS.QUIZ_BACKGROUND_TOGGLE, true);
  const [selectedLanguage, setSelectedLanguage] = useLocalStorage<string>(STORAGE_KEYS.QUIZ_LANGUAGE, 'en');

  // Quiz state management
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [score, setScore] = useState(0);
  const [showScore, setShowScore] = useState(false);
  const [selectedAnswer, setSelectedAnswer] = useState<null | boolean>(null);
  const [feedbackMessage, setFeedbackMessage] = useState('');
  const [answersDisabled, setAnswersDisabled] = useState(false);
  const [explanation, setExplanation] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [quizQuestions, setQuizQuestions] = useState<QuizQuestion[]>([]);
  const [customCategoryInput, setCustomCategoryInput] = useState('');
  const [selectedMode, setSelectedMode] = useState<string | null>(null);

  // API hook
  const { loading: loadingQuestions, error: questionsError, generateQuestions, getExplanation } = useGeminiAPI();
  const [loadingExplanation, setLoadingExplanation] = useState(false);

  // Helper function to get translations
  const t = (key: keyof typeof translations.en) => {
    return translations[selectedLanguage as keyof typeof translations][key];
  };

  // Function to toggle background usage
  const toggleBackgroundUsage = () => {
    setUseBackground(!useBackground);
  };

  // Function to toggle language
  const toggleLanguage = () => {
    const newLanguage = selectedLanguage === 'en' ? 'ar' : 'en';
    setSelectedLanguage(newLanguage);
  };

  // Function to handle answer option clicks
  const handleAnswerOptionClick = (isCorrect: boolean) => {
    if (answersDisabled) return;
    setSelectedAnswer(isCorrect);
    setAnswersDisabled(true);
    if (isCorrect) {
      setScore(score + 1);
      setFeedbackMessage(t('correct'));
    } else {
      setFeedbackMessage(t('incorrect'));
    }
  };

  // Function to move to the next question
  const handleNextQuestion = () => {
    const nextQuestion = currentQuestion + 1;
    if (nextQuestion < quizQuestions.length) {
      setCurrentQuestion(nextQuestion);
      setSelectedAnswer(null);
      setFeedbackMessage('');
      setAnswersDisabled(false);
      setExplanation('');
    } else {
      setShowScore(true);
    }
  };

  // Function to reset the quiz and go back to category selection
  const resetQuizAndGoToCategories = () => {
    setCurrentQuestion(0);
    setScore(0);
    setShowScore(false);
    setSelectedAnswer(null);
    setFeedbackMessage('');
    setAnswersDisabled(false);
    setExplanation('');
    setQuizQuestions([]);
    setSelectedCategory(null);
    setCustomCategoryInput('');
    setSelectedMode(null);
  };

  // Function to get explanation from Gemini API
  const handleExplainAnswer = async () => {
    setLoadingExplanation(true);
    setExplanation('');
    const currentQuestionData = quizQuestions[currentQuestion];
    const correctAnswer = currentQuestionData.answerOptions.find((opt: AnswerOption) => opt.isCorrect)?.answerText;

    try {
      const explanationText = await getExplanation(currentQuestionData.questionText, correctAnswer || '', selectedLanguage);
      setExplanation(explanationText);
    } catch {
      setExplanation('Failed to fetch explanation. Please try again.');
    } finally {
      setLoadingExplanation(false);
    }
  };

  // Function to fetch questions from Gemini API based on category
  const fetchQuestions = async (category: string) => {
    setSelectedCategory(category);
    setCurrentQuestion(0);
    setScore(0);
    setShowScore(false);
    setSelectedAnswer(null);
    setFeedbackMessage('');
    setAnswersDisabled(false);
    setExplanation('');

    try {
      const questions = await generateQuestions(category, selectedLanguage);
      setQuizQuestions(questions);
    } catch {
      // Error is already handled by the hook
      console.error('Failed to fetch questions');
    }
  };

  const handleCustomCategorySubmit = () => {
    fetchQuestions(customCategoryInput);
  };

  // Create container style and classes using utility functions
  const containerStyle = getContainerStyle(background, useBackground);
  const containerClasses = getContainerClasses(background, useBackground);

  return (
    <div className={containerClasses} style={containerStyle} dir={selectedLanguage === 'ar' ? 'rtl' : 'ltr'}>
      <Link to="/" className={`absolute top-4 ${selectedLanguage === 'ar' ? 'right-4' : 'left-4'} bg-gradient-to-r from-yellow-400 to-yellow-200 text-black font-bold py-2 px-5 rounded-full shadow-md hover:scale-105 transition-all duration-300 border-2 border-yellow-500`}>{t('home')}</Link>

      {/* Language switcher button */}
      <button
        onClick={toggleLanguage}
        className={`absolute top-4 ${selectedLanguage === 'ar' ? 'left-16' : 'right-16'} bg-gradient-to-r from-purple-400 to-purple-600 text-white p-2 rounded-full shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 z-10 flex items-center gap-1`}
        title={selectedLanguage === 'en' ? t('switchToArabic') : t('switchToEnglish')}
      >
        <Languages size={16} />
        <span className="text-sm font-medium">
          {languages.find((lang: Language) => lang.code === selectedLanguage)?.flag}
        </span>
      </button>

      {/* Background toggle button in top right corner */}
      {background && (
        <button
          onClick={toggleBackgroundUsage}
          className={`absolute top-4 ${selectedLanguage === 'ar' ? 'left-4' : 'right-4'} bg-gradient-to-r from-blue-400 to-blue-600 text-white p-2 rounded-full shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 z-10`}
          title={useBackground ? t('disableBackground') : t('enableBackground')}
        >
          <Image size={20} />
        </button>
      )}
      <div className="bg-white rounded-3xl shadow-2xl p-8 w-full max-w-xl border-4 border-yellow-300 relative">
        <div className="text-center mb-8">
          <h2 className="text-4xl md:text-5xl font-extrabold gradient-red-blue inline-block text-transparent bg-clip-text mb-2">Football Quiz</h2>
          <p className="text-lg text-gray-600 font-medium">{t('testKnowledge')}</p>
        </div>
        {/* Mode Selection */}
        {!selectedMode && (
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-6">{t('chooseMode')}</h2>
            <div className="flex flex-col gap-4 justify-center items-center">
              <button
                onClick={() => setSelectedMode('Single Player')}
                className="py-3 px-6 text-lg font-bold rounded-full shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 focus:outline-none flex items-center justify-center gradient-yellow"
                style={{ minWidth: '200px' }}
              >
                <User size={20} className={selectedLanguage === 'ar' ? 'ml-2' : 'mr-2'} />
                {t('singlePlayer')}
              </button>
              <button
                onClick={() => setSelectedMode('Multi-Player')}
                className="py-3 px-6 text-lg font-bold rounded-full shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 focus:outline-none flex items-center justify-center bg-gray-200 text-gray-800 hover:bg-gray-300"
                style={{ minWidth: '200px' }}
              >
                <Users size={20} className={selectedLanguage === 'ar' ? 'ml-2' : 'mr-2'} />
                {t('multiPlayer')}
              </button>
            </div>
          </div>
        )}
        {/* Multi-Player Coming Soon */}
        {selectedMode === 'Multi-Player' && (
          <div className="text-center mt-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">{t('multiPlayer')}</h2>
            <p className="text-xl text-gray-700 mb-6">{t('comingSoon')}</p>
            <button
              onClick={() => setSelectedMode(null)}
              className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-6 rounded-xl transition duration-300 ease-in-out shadow-lg"
            >
              {t('back')}
            </button>
          </div>
        )}
        {/* Single Player Quiz Logic */}
        {selectedMode === 'Single Player' && (
          !selectedCategory || quizQuestions.length === 0 && !loadingQuestions && !questionsError ? (
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-800 mb-6">{t('chooseCategory')}</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                {categories[selectedLanguage as keyof typeof categories].map((category: string) => (
                  <button
                    key={category}
                    onClick={() => fetchQuestions(category)}
                    className="font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg text-yellow-100 border-yellow-300"
                    style={{ background: 'linear-gradient(110deg, var(--barça-blue) 50%, var(--barça-red) 50%)' }}
                  >
                    {category}
                  </button>
                ))}
              </div>
              <div className="mt-8">
                <h3 className="text-xl font-bold text-gray-800 mb-4">{t('customCategory')}</h3>
                <input
                  type="text"
                  value={customCategoryInput}
                  onChange={(e) => setCustomCategoryInput(e.target.value)}
                  placeholder={t('customCategoryPlaceholder')}
                  className="w-full p-3 border-2 border-gray-300 rounded-xl focus:outline-none focus:border-blue-500 mb-4 text-gray-800"
                  dir={selectedLanguage === 'ar' ? 'rtl' : 'ltr'}
                />
                <button
                  onClick={handleCustomCategorySubmit}
                  className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg border-2"
                  disabled={!customCategoryInput.trim()}
                >
                  {t('startCustomQuiz')}
                </button>
              </div>
              {questionsError && (
                <p className="text-red-500 mt-4 text-sm">{questionsError}</p>
              )}
              <button
                onClick={() => setSelectedMode(null)}
                className="w-full mt-4 text-gray-700 font-bold py-2 px-6 rounded-xl transition duration-300"
              >
                {t('backToSelectMode')}
              </button>
            </div>
          ) : loadingQuestions ? (
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-800 mb-4">{t('loadingQuestions')}</h2>
              <p className="text-gray-600">{t('loadingQuestionsDesc')}</p>
              <div className="flex justify-center items-center mt-6">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
              </div>
            </div>
          ) : showScore ? (
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-800 mb-4">{t('quizCompleted')}</h2>
              <p className="text-xl text-gray-700 mb-6">
                {t('youScored')} <span className="font-bold text-purple-700">{score}</span> {t('outOf')} <span className="font-bold text-purple-700">{quizQuestions.length}</span>
              </p>
              <button
                onClick={resetQuizAndGoToCategories}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg mb-4"
              >
                {t('playAgain')}
              </button>
              <Link to="/" className="w-full block bg-yellow-400 hover:bg-yellow-500 text-black font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg border-2 border-yellow-500">{t('backToHome')}</Link>
            </div>
          ) : (
            <div>
              <div className={`flex justify-between items-center mb-6 ${selectedLanguage === 'ar' ? 'flex-row-reverse' : ''}`}>
                <div className="text-lg font-semibold text-gray-600">
                  {t('question')} <span className="font-bold text-blue-700">{currentQuestion + 1}</span> / {quizQuestions.length}
                </div>
                <button
                  onClick={resetQuizAndGoToCategories}
                  className="bg-red-500 hover:bg-red-600 text-white text-sm font-bold py-2 px-4 rounded-xl transition duration-300 ease-in-out shadow-md"
                >
                  {t('cancelQuiz')}
                </button>
              </div>
              <div className="text-2xl font-bold text-gray-800 mb-6 bg-gradient-to-r from-blue-100 to-purple-100 rounded-lg p-4 shadow-inner">
                {quizQuestions[currentQuestion]?.questionText}
              </div>
              <div className="space-y-4 mb-6">
                {quizQuestions[currentQuestion]?.answerOptions.map((answerOption: AnswerOption, index: number) => (
                  <button
                    key={index}
                    onClick={() => handleAnswerOptionClick(answerOption.isCorrect)}
                    className={`
                      w-full ${selectedLanguage === 'ar' ? 'text-right' : 'text-left'} py-3 px-4 rounded-xl border-2
                      transition duration-200 ease-in-out font-semibold text-lg
                      ${
                        selectedAnswer === null
                          ? 'bg-gray-100 hover:bg-gray-200 border-gray-300 text-gray-800'
                          : answerOption.isCorrect
                            ? 'bg-green-100 border-green-500 text-green-800'
                            : selectedAnswer !== null && !answerOption.isCorrect && selectedAnswer === false && selectedAnswer !== undefined
                              ? 'bg-red-100 border-red-500 text-red-800'
                              : 'bg-gray-100 border-gray-300 text-gray-800 opacity-70 cursor-not-allowed'
                      }
                      ${answersDisabled ? 'cursor-not-allowed' : 'cursor-pointer'}
                    `}
                    disabled={answersDisabled}
                  >
                    {answerOption.answerText}
                  </button>
                ))}
              </div>
              {feedbackMessage && (
                <div
                  className={`text-center text-lg font-bold mb-4
                    ${selectedAnswer ? 'text-green-600' : 'text-red-600'}
                  `}
                >
                  {feedbackMessage}
                </div>
              )}
              {selectedAnswer !== null && (
                <div className="mt-4">
                  <button
                    onClick={handleExplainAnswer}
                    className="w-full bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg mb-4"
                    disabled={loadingExplanation}
                  >
                    {loadingExplanation ? t('loadingExplanation') : t('explainAnswer')}
                  </button>
                  {explanation && (
                    <div className="bg-gray-50 p-4 rounded-lg text-gray-700 text-base border border-gray-200" dir={selectedLanguage === 'ar' ? 'rtl' : 'ltr'}>
                      <h3 className="font-semibold text-base mb-2">{t('explanation')}</h3>
                      <p>{explanation}</p>
                    </div>
                  )}
                </div>
              )}
              <button
                onClick={handleNextQuestion}
                className={`w-full font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg mt-6
                  ${selectedAnswer !== null ? 'bg-purple-600 hover:bg-purple-700 text-white' : 'bg-gray-300 text-gray-600 cursor-not-allowed'}
                `}
                disabled={selectedAnswer === null}
              >
                {currentQuestion === quizQuestions.length - 1 ? t('finishQuiz') : t('nextQuestion')}
              </button>
            </div>
          )
        )}
      </div>
    </div>
  );
};

export default Quiz;